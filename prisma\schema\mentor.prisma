model mentor {
  id            String    @id @default(cuid())
  name          String
  email         String    @unique
  emailVerified Boolean   @default(false)
  image         String
  password      String?
  location      String?
  designation   String
  desc          String
  order         Int?
  linkedin      String?
  profile       String?
  status        Status    @default(Active)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  reviews       review[]
  servicesId    String?
  services      service[]
}

model service {
  id           String                @id @default(cuid())
  name         String
  price        Int
  description  String
  meeting_link String
  createdAt    DateTime              @default(now())
  updatedAt    DateTime              @updatedAt
  status       Status                @default(Active)
  mentor       mentor?               @relation(fields: [mentorId], references: [id], onDelete: SetNull)
  mentorId     String?
  users        user_mentor_service[]
  guest        guest_mentor_service[]
  payments     payment[]             // New unified payment relation
}
