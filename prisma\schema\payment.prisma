/**
 * Unified Payment Model
 * 
 * This model consolidates all payment-related data from the previous 8 separate tables
 * into a single unified payment table. This design improves maintainability, reduces
 * code duplication, and simplifies payment management across all service types.
 * 
 * Supported Service Types:
 * - mentor: Career consultation and mentorship services
 * - package: Bundled service offerings
 * - immigration: Immigration and visa services
 * - training: Educational training programs
 * 
 * Payment Types:
 * - user: Authenticated user payments (linked to user account)
 * - guest: Guest user payments (contact info stored directly)
 */

model payment {
  id                     String               @id @default(cuid())
  amount                 Int
  status                 String
  payment_type           String               // 'user' or 'guest'
  service_type           String               // 'mentor', 'package', 'immigration', 'training'
  progress               Status               @default(Pending)

  // User reference (nullable for guest payments)
  user                   user?                @relation(fields: [userId], references: [id], onDelete: SetNull)
  userId                 String?

  // Service references (only one will be populated per record)
  service                service?             @relation(fields: [serviceId], references: [id], onDelete: SetNull)
  serviceId              String?
  package                packages?            @relation(fields: [packageId], references: [id], onDelete: SetNull)
  packageId              String?
  immigration_service    immigration_service? @relation(fields: [immigration_serviceId], references: [id], onDelete: SetNull)
  immigration_serviceId  String?
  training               training?            @relation(fields: [trainingId], references: [id], onDelete: SetNull)
  trainingId             String?

  // Guest contact information (nullable for user payments)
  guest_name             String?
  guest_email            String?
  guest_mobile           String?

  // Stripe payment information
  stripe_session_id      String?
  stripe_payment_intent_id String?

  createdAt              DateTime             @default(now())
  updatedAt              DateTime             @updatedAt

  // Indexes for performance optimization
  @@index([userId])
  @@index([service_type])
  @@index([payment_type])
  @@index([status])
  @@index([createdAt])
  @@index([stripe_session_id])
  @@index([stripe_payment_intent_id])
}
