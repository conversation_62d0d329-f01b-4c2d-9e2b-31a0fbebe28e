# Payment Test Data Scripts

This directory contains scripts for managing payment test data and migration processes.

## 📋 Available Scripts

### 1. Payment Test Data Insertion

**Script:** `insert-payment-test-data.ts`  
**Command:** `npm run seed:payment-data`

Inserts approximately 500 test records across all 8 payment tables to simulate realistic payment data for testing the payment migration.

#### Data Distribution:
- **User Payment Tables:** ~250 records (62-63 per table)
  - `user_mentor_service`: 62 records
  - `user_package`: 62 records  
  - `user_immigration_service`: 62 records
  - `user_training`: 62 records

- **Guest Payment Tables:** ~250 records (62-66 per table)
  - `guest_mentor_service`: 62 records
  - `guest_package`: 62 records
  - `guest_immigration_service`: 62 records
  - `guest_training`: 66 records

#### Generated Data Features:
- **Realistic Amounts:** €50 - €500 (5000-50000 cents)
- **Random Dates:** Within the last 6 months
- **Payment Statuses:** paid, pending, failed, refunded
- **Progress Statuses:** Pending, Active, Inactive
- **Guest Information:** 20 different realistic guest profiles
- **Foreign Key References:** Sample IDs for users, services, packages, etc.

#### Usage:
```bash
# Install dependencies first
npm install

# Run the script
npm run seed:payment-data
```

#### Safety Features:
- **Non-destructive:** Won't delete existing data
- **Existing Data Check:** Shows current record counts before proceeding
- **5-second delay:** Allows cancellation if existing data is found
- **Progress Logging:** Shows insertion progress for each table
- **Final Verification:** Displays final record counts after completion

### 2. Payment Migration Scripts

#### Migration Script
**Script:** `migrate-payments.ts`  
**Command:** `npm run migrate:payments`

Migrates data from the 8 separate payment tables to the unified payment table.

#### Validation Script
**Script:** `validate-payment-migration.ts`  
**Command:** `npm run validate:payment-migration`

Validates that the migration was successful by comparing record counts and data integrity.

#### Rollback Script
**Script:** `rollback-payment-migration.ts`  
**Command:** `npm run rollback:payment-migration`

Rolls back the payment migration by restoring data to the original 8 tables.

## 🚀 Quick Start Guide

### Step 1: Prepare Test Environment
```bash
# Ensure database is running and accessible
npm run prisma:generate

# Check current database state
npm run prisma:studio
```

### Step 2: Insert Test Data
```bash
# Insert ~500 test payment records
npm run seed:payment-data
```

### Step 3: Test Migration (Optional)
```bash
# Run the payment migration
npm run migrate:payments

# Validate the migration
npm run validate:payment-migration

# If needed, rollback
npm run rollback:payment-migration
```

## 📊 Sample Data Structure

### User Payment Record Example:
```typescript
{
  id: "cl9x8y7z6w5v4u3t2s1r",
  amount: 15000, // €150.00 in cents
  status: "paid",
  progress: "Active",
  userId: "user_001",
  serviceId: "service_003", // For mentor services
  packageId: "package_002", // For packages
  immigration_serviceId: "immigration_005", // For immigration
  trainingId: "training_001", // For training
  createdAt: "2024-01-15T10:30:00.000Z",
  updatedAt: "2024-01-15T11:45:00.000Z"
}
```

### Guest Payment Record Example:
```typescript
{
  id: "cl9x8y7z6w5v4u3t2s1s",
  amount: 25000, // €250.00 in cents
  status: "paid",
  name: "John Smith",
  email: "<EMAIL>",
  mobile_no: "+353-1-234-5678",
  progress: "Pending",
  serviceId: "service_007",
  createdAt: "2024-02-20T14:15:00.000Z",
  updatedAt: "2024-02-20T14:30:00.000Z"
}
```

## 🔧 Customization

### Modifying Data Volume
Edit the `recordsPerTable` variable in `insert-payment-test-data.ts`:

```typescript
// Current: 62 records per table (~500 total)
const recordsPerTable = 62;

// For more data: 125 records per table (~1000 total)
const recordsPerTable = 125;

// For less data: 25 records per table (~200 total)
const recordsPerTable = 25;
```

### Adding Custom Sample Data
Update the sample arrays in the script:

```typescript
// Add more sample users
const sampleUsers = [
  'user_001', 'user_002', // ... existing users
  'user_021', 'user_022', // ... add new users
];

// Add more guest profiles
const guestNames = [
  'John Smith', 'Sarah Johnson', // ... existing names
  'New Guest Name', // ... add new names
];
```

### Modifying Date Ranges
Adjust the `getRandomDate()` function:

```typescript
function getRandomDate(): Date {
  const now = new Date();
  // Change from 6 months to 1 year
  const oneYearAgo = new Date(now.getTime() - (12 * 30 * 24 * 60 * 60 * 1000));
  const randomTime = oneYearAgo.getTime() + Math.random() * (now.getTime() - oneYearAgo.getTime());
  return new Date(randomTime);
}
```

## ⚠️ Important Notes

1. **Database Connection:** Ensure your database is running and accessible before running scripts
2. **Prisma Schema:** Make sure your Prisma schema is up to date with `npm run prisma:generate`
3. **Foreign Key Constraints:** The script uses sample IDs that may not exist in your database. For production use, query actual IDs from related tables
4. **Performance:** Inserting 500 records may take 1-2 minutes depending on your database performance
5. **Memory Usage:** The script processes records sequentially to avoid memory issues

## 🐛 Troubleshooting

### Common Issues:

#### Foreign Key Constraint Errors
```bash
Error: Foreign key constraint failed
```
**Solution:** The sample IDs don't exist in your database. Either:
- Create the referenced records first, or
- Modify the script to use actual IDs from your database

#### Connection Timeout
```bash
Error: Connection timeout
```
**Solution:** 
- Check database connection settings in `.env`
- Ensure database server is running
- Increase timeout in Prisma configuration

#### Out of Memory
```bash
Error: JavaScript heap out of memory
```
**Solution:**
- Reduce `recordsPerTable` value
- Run script with increased memory: `node --max-old-space-size=4096`

### Getting Help

1. Check the console output for detailed error messages
2. Verify database connection with `npm run prisma:studio`
3. Review the Prisma schema for table structure
4. Check existing data counts before running scripts

## 📝 Script Logs

The scripts provide detailed logging:

```
🚀 Starting payment test data insertion...
📊 Target: ~500 records across 8 payment tables

Inserting 62 user_mentor_service records...
✅ Inserted 62 user_mentor_service records

Inserting 62 user_package records...
✅ Inserted 62 user_package records

...

🎉 Payment test data insertion completed!
📈 Summary:
   User payment tables: 248 records
   Guest payment tables: 252 records
   Total inserted: 500 records

📊 Final table counts:
   userMentorService: 62 records
   guestMentorService: 62 records
   userPackage: 62 records
   guestPackage: 62 records
   userImmigrationService: 62 records
   guestImmigrationService: 62 records
   userTraining: 62 records
   guestTraining: 66 records
   Total: 500 records
```
