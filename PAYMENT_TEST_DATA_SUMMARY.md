# Payment Test Data Scripts - Implementation Summary

## 📋 Overview

I've created a comprehensive set of scripts to insert realistic test data into all 8 payment tables in your Career Ireland API. This will help you test the payment migration process with substantial data.

## 🎯 What Was Created

### 1. Main Test Data Script
**File:** `scripts/insert-payment-test-data.ts`
- **Purpose:** Inserts ~500 realistic payment records across all 8 payment tables
- **Distribution:** 62-66 records per table
- **Features:** Realistic amounts, dates, statuses, and guest information

### 2. Cleanup Script
**File:** `scripts/cleanup-payment-test-data.ts`
- **Purpose:** Safely removes all test payment data
- **Safety:** 10-second confirmation delay before deletion
- **Verification:** Shows before/after record counts

### 3. Documentation
**File:** `scripts/README.md`
- **Purpose:** Comprehensive guide for using all payment scripts
- **Includes:** Usage instructions, troubleshooting, customization options

### 4. Package.json Scripts
Added two new npm scripts:
- `npm run seed:payment-data` - Insert test data
- `npm run cleanup:payment-data` - Remove test data

## 📊 Test Data Specifications

### Data Volume
- **Total Records:** ~500 across 8 tables
- **User Tables:** 248 records (4 tables × 62 records each)
- **Guest Tables:** 252 records (3 tables × 62 + 1 table × 66)

### Data Characteristics

#### Payment Amounts
- **Range:** €50 - €500 (5,000 - 50,000 cents)
- **Distribution:** Random within range
- **Realistic:** Typical service pricing

#### Dates
- **Range:** Last 6 months
- **Pattern:** Random distribution
- **Timestamps:** Realistic created/updated times

#### Payment Statuses
- `paid` (most common)
- `pending`
- `failed`
- `refunded`

#### Progress Statuses
- `Pending`
- `Active`
- `Inactive`

#### Guest Information
- **20 unique guest profiles** with realistic:
  - Names (John Smith, Sarah Johnson, etc.)
  - Email addresses (<EMAIL>, etc.)
  - Irish phone numbers (+353-1-234-xxxx)

#### Foreign Key References
- **Sample User IDs:** user_001 through user_020
- **Sample Service IDs:** service_001 through service_010
- **Sample Package IDs:** package_001 through package_010
- **Sample Immigration Service IDs:** immigration_001 through immigration_010
- **Sample Training IDs:** training_001 through training_010

## 🚀 How to Use

### Step 1: Install Dependencies
```bash
npm install
```

### Step 2: Generate Prisma Client
```bash
npm run prisma:generate
```

### Step 3: Insert Test Data
```bash
npm run seed:payment-data
```

### Step 4: Verify Data (Optional)
```bash
npm run prisma:studio
```

### Step 5: Clean Up (When Done)
```bash
npm run cleanup:payment-data
```

## 📋 Table Breakdown

### User Payment Tables (248 records)
1. **user_mentor_service** - 62 records
   - User purchases of mentor consultation services
   - Links to user and service tables

2. **user_package** - 62 records
   - User purchases of service packages
   - Links to user and packages tables

3. **user_immigration_service** - 62 records
   - User purchases of immigration services
   - Links to user and immigration_service tables

4. **user_training** - 62 records
   - User purchases of training programs
   - Links to user and training tables

### Guest Payment Tables (252 records)
1. **guest_mentor_service** - 62 records
   - Guest purchases of mentor services
   - Includes guest contact information

2. **guest_package** - 62 records
   - Guest purchases of service packages
   - Includes guest contact information

3. **guest_immigration_service** - 62 records
   - Guest purchases of immigration services
   - Includes guest contact information

4. **guest_training** - 66 records
   - Guest purchases of training programs
   - Includes guest contact information

## ⚠️ Important Notes

### Foreign Key Dependencies
The script uses sample IDs that may not exist in your database:
- `user_001` through `user_020`
- `service_001` through `service_010`
- `package_001` through `package_010`
- `immigration_001` through `immigration_010`
- `training_001` through `training_010`

**For production use:** Modify the script to query actual IDs from your database.

### Safety Features
1. **Non-destructive:** Won't delete existing data
2. **Existing data check:** Shows current counts before proceeding
3. **Confirmation delays:** 5 seconds for insertion, 10 seconds for cleanup
4. **Progress logging:** Real-time feedback during execution
5. **Final verification:** Shows record counts after completion

### Performance Considerations
- **Execution time:** 1-2 minutes for 500 records
- **Memory usage:** Minimal (sequential processing)
- **Database load:** Moderate (one record at a time)

## 🔧 Customization Options

### Modify Record Count
```typescript
// In insert-payment-test-data.ts
const recordsPerTable = 62; // Change this value
```

### Add More Sample Data
```typescript
// Add more users, services, etc.
const sampleUsers = [
  'user_001', 'user_002', // ... existing
  'user_021', 'user_022', // ... add more
];
```

### Change Date Range
```typescript
// Modify getRandomDate() function
const sixMonthsAgo = new Date(now.getTime() - (6 * 30 * 24 * 60 * 60 * 1000));
// Change to 1 year: (12 * 30 * 24 * 60 * 60 * 1000)
```

### Adjust Amount Range
```typescript
// Modify getRandomAmount() function
return Math.floor(Math.random() * 45000) + 5000; // €50-€500
// Change to €100-€1000: Math.floor(Math.random() * 90000) + 10000;
```

## 🧪 Testing the Migration

### Recommended Testing Flow
1. **Insert test data:** `npm run seed:payment-data`
2. **Run migration:** `npm run migrate:payments`
3. **Validate migration:** `npm run validate:payment-migration`
4. **Test rollback:** `npm run rollback:payment-migration`
5. **Clean up:** `npm run cleanup:payment-data`

### Verification Queries
```sql
-- Check total records in each table
SELECT 'user_mentor_service' as table_name, COUNT(*) as count FROM user_mentor_service
UNION ALL
SELECT 'guest_mentor_service', COUNT(*) FROM guest_mentor_service
UNION ALL
SELECT 'user_package', COUNT(*) FROM user_package
UNION ALL
SELECT 'guest_package', COUNT(*) FROM guest_package
UNION ALL
SELECT 'user_immigration_service', COUNT(*) FROM user_immigration_service
UNION ALL
SELECT 'guest_immigration_service', COUNT(*) FROM guest_immigration_service
UNION ALL
SELECT 'user_training', COUNT(*) FROM user_training
UNION ALL
SELECT 'guest_training', COUNT(*) FROM guest_training;

-- Check payment status distribution
SELECT status, COUNT(*) as count 
FROM user_mentor_service 
GROUP BY status;

-- Check date range
SELECT 
  MIN(createdAt) as earliest_date,
  MAX(createdAt) as latest_date,
  COUNT(*) as total_records
FROM user_mentor_service;
```

## 🐛 Troubleshooting

### Common Issues

1. **Foreign Key Errors**
   - **Cause:** Sample IDs don't exist in your database
   - **Solution:** Create referenced records or modify script to use actual IDs

2. **Connection Timeout**
   - **Cause:** Database connection issues
   - **Solution:** Check `.env` file and database server status

3. **Memory Issues**
   - **Cause:** Large data volume
   - **Solution:** Reduce `recordsPerTable` value

### Getting Help
- Check console output for detailed error messages
- Use `npm run prisma:studio` to inspect database state
- Review the `scripts/README.md` for detailed troubleshooting

## ✅ Success Criteria

After running the script successfully, you should see:
- **500 total records** across all 8 payment tables
- **Realistic data distribution** with varied amounts, dates, and statuses
- **No foreign key constraint errors**
- **Proper progress logging** throughout execution
- **Final verification** showing exact record counts

This test data will provide a solid foundation for testing your payment migration process with realistic volume and data patterns.
